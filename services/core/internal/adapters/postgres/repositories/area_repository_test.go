package repositories

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/paradoxe35/torra/packages/database"
	"github.com/paradoxe35/torra/packages/errors"
	"github.com/paradoxe35/torra/services/core/internal/domain/entities"
	"github.com/paradoxe35/torra/services/core/internal/domain/valueobjects"
)

func setupAreaRepo(t *testing.T) (*areaRepository, func()) {
	// Skip test if database testing is not enabled
	if !database.IsTestingEnabled() {
		t.Skip("Skipping test: database testing not enabled. Set ENABLE_DB_TESTS=true or run in CI")
	}

	// Setup test database
	testDB, err := database.SetupTestDB(nil) // Use default config
	if err != nil {
		t.Fatalf("Failed to setup test database: %v", err)
	}

	repo := &areaRepository{db: testDB.DB}

	cleanup := func() {
		if err := testDB.Close(); err != nil {
			t.<PERSON><PERSON><PERSON>("Failed to cleanup test database: %v", err)
		}
	}

	return repo, cleanup
}

func createTestArea(t *testing.T, areaType valueobjects.AreaType) *entities.Area {
	return &entities.Area{
		ID:         uuid.New(),
		ParentID:   nil,
		Type:       areaType,
		Name:       "Test Area",
		Code:       "TEST001",
		Boundaries: nil,
		CenterLat:  40.7128,
		CenterLng:  -74.0060,
		RadiusKm:   10.0,
		Status:     valueobjects.AreaStatusActive,
		Metadata:   nil,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}
}

func TestAreaRepository_Save(t *testing.T) {
	repo, cleanup := setupAreaRepo(t)
	defer cleanup()

	ctx := context.Background()
	area := createTestArea(t, valueobjects.AreaTypeCity)

	// Test: Save area
	err := repo.Save(ctx, area)
	assert.NoError(t, err)

	// Test: Verify area was saved
	savedArea, err := repo.FindByID(ctx, area.ID)
	assert.NoError(t, err)
	assert.NotNil(t, savedArea)
	assert.Equal(t, area.ID, savedArea.ID)
	assert.Equal(t, area.Name, savedArea.Name)
	assert.Equal(t, area.Code, savedArea.Code)
}

func TestAreaRepository_FindByID(t *testing.T) {
	repo, cleanup := setupAreaRepo(t)
	defer cleanup()

	ctx := context.Background()
	area := createTestArea(t, valueobjects.AreaTypeCity)

	// Create area first
	err := repo.Save(ctx, area)
	require.NoError(t, err)

	// Test: Find existing area
	foundArea, err := repo.FindByID(ctx, area.ID)
	assert.NoError(t, err)
	assert.NotNil(t, foundArea)
	assert.Equal(t, area.ID, foundArea.ID)
	assert.Equal(t, area.Name, foundArea.Name)

	// Test: Find non-existent area
	_, err = repo.FindByID(ctx, uuid.New())
	assert.Error(t, err)
}

func TestAreaRepository_FindByCode(t *testing.T) {
	repo, cleanup := setupAreaRepo(t)
	defer cleanup()

	ctx := context.Background()
	area := createTestArea(t, valueobjects.AreaTypeCity)

	// Create area first
	err := repo.Save(ctx, area)
	require.NoError(t, err)

	// Test: Find existing area by code
	foundArea, err := repo.FindByCode(ctx, area.Code)
	assert.NoError(t, err)
	assert.NotNil(t, foundArea)
	assert.Equal(t, area.ID, foundArea.ID)
	assert.Equal(t, area.Code, foundArea.Code)

	// Test: Find non-existent area by code
	_, err = repo.FindByCode(ctx, "NONEXISTENT")
	assert.Error(t, err)
}

func TestAreaRepository_FindByParentID(t *testing.T) {
	repo, cleanup := setupAreaRepo(t)
	defer cleanup()

	ctx := context.Background()

	// Create parent area
	parentArea := createTestArea(t, valueobjects.AreaTypeCountry)
	parentArea.Name = "Parent Country"
	parentArea.Code = "PARENT001"
	err := repo.Save(ctx, parentArea)
	require.NoError(t, err)

	// Create child areas
	childArea1 := createTestArea(t, valueobjects.AreaTypeCity)
	childArea1.ParentID = &parentArea.ID
	childArea1.Name = "Child City 1"
	childArea1.Code = "CHILD001"

	childArea2 := createTestArea(t, valueobjects.AreaTypeCity)
	childArea2.ParentID = &parentArea.ID
	childArea2.Name = "Child City 2"
	childArea2.Code = "CHILD002"

	err = repo.Save(ctx, childArea1)
	require.NoError(t, err)
	err = repo.Save(ctx, childArea2)
	require.NoError(t, err)

	// Test: Find children by parent ID
	children, err := repo.FindByParentID(ctx, &parentArea.ID)
	assert.NoError(t, err)
	assert.Len(t, children, 2)

	// Verify children belong to parent
	for _, child := range children {
		assert.NotNil(t, child.ParentID)
		assert.Equal(t, parentArea.ID, *child.ParentID)
	}

	// Test: Find root areas (no parent)
	rootAreas, err := repo.FindByParentID(ctx, nil)
	assert.NoError(t, err)
	assert.GreaterOrEqual(t, len(rootAreas), 1) // At least the parent area
}

func TestAreaRepository_FindByType(t *testing.T) {
	repo, cleanup := setupAreaRepo(t)
	defer cleanup()

	ctx := context.Background()

	// Create areas of different types
	countryArea := createTestArea(t, valueobjects.AreaTypeCountry)
	countryArea.Name = "Test Country"
	countryArea.Code = "COUNTRY001"

	cityArea := createTestArea(t, valueobjects.AreaTypeCity)
	cityArea.Name = "Test City"
	cityArea.Code = "CITY001"

	err := repo.Save(ctx, countryArea)
	require.NoError(t, err)
	err = repo.Save(ctx, cityArea)
	require.NoError(t, err)

	// Test: Find areas by type
	countries, err := repo.FindByType(ctx, valueobjects.AreaTypeCountry)
	assert.NoError(t, err)
	assert.GreaterOrEqual(t, len(countries), 1)

	cities, err := repo.FindByType(ctx, valueobjects.AreaTypeCity)
	assert.NoError(t, err)
	assert.GreaterOrEqual(t, len(cities), 1)

	// Verify types are correct
	for _, country := range countries {
		assert.Equal(t, valueobjects.AreaTypeCountry, country.Type)
	}
	for _, city := range cities {
		assert.Equal(t, valueobjects.AreaTypeCity, city.Type)
	}
}

func TestAreaRepository_FindActive(t *testing.T) {
	repo, cleanup := setupAreaRepo(t)
	defer cleanup()

	ctx := context.Background()

	// Create active area
	activeArea := createTestArea(t, valueobjects.AreaTypeCity)
	activeArea.Status = valueobjects.AreaStatusActive
	activeArea.Name = "Active Area"
	activeArea.Code = "ACTIVE001"

	// Create inactive area
	inactiveArea := createTestArea(t, valueobjects.AreaTypeCity)
	inactiveArea.Status = valueobjects.AreaStatusInactive
	inactiveArea.Name = "Inactive Area"
	inactiveArea.Code = "INACTIVE001"

	err := repo.Save(ctx, activeArea)
	require.NoError(t, err)
	err = repo.Save(ctx, inactiveArea)
	require.NoError(t, err)

	// Test: Find only active areas
	activeAreas, err := repo.FindActive(ctx)
	assert.NoError(t, err)
	assert.GreaterOrEqual(t, len(activeAreas), 1)

	// Verify all returned areas are active
	for _, area := range activeAreas {
		assert.Equal(t, valueobjects.AreaStatusActive, area.Status)
	}
}

func TestAreaRepository_FindByCoordinate(t *testing.T) {
	repo, cleanup := setupAreaRepo(t)
	defer cleanup()

	ctx := context.Background()

	// Create area with specific coordinates
	area := createTestArea(t, valueobjects.AreaTypeCity)
	area.CenterLat = 40.7128  // New York coordinates
	area.CenterLng = -74.0060
	area.RadiusKm = 50.0 // Large radius to ensure it covers the test point
	area.Name = "New York Area"
	area.Code = "NYC001"

	err := repo.Save(ctx, area)
	require.NoError(t, err)

	// Test: Find areas by coordinate (within the area)
	areas, err := repo.FindByCoordinate(ctx, 40.7128, -74.0060)
	assert.NoError(t, err)
	assert.GreaterOrEqual(t, len(areas), 1)

	// Test: Find areas by coordinate (far away - should return empty or distant areas)
	distantAreas, err := repo.FindByCoordinate(ctx, 0.0, 0.0)
	assert.NoError(t, err)
	// This might return areas ordered by distance, so we don't assert on count
}

func TestAreaRepository_Update(t *testing.T) {
	repo, cleanup := setupAreaRepo(t)
	defer cleanup()

	ctx := context.Background()
	area := createTestArea(t, valueobjects.AreaTypeCity)

	// Create area first
	err := repo.Save(ctx, area)
	require.NoError(t, err)

	// Update area
	area.Name = "Updated Area Name"
	area.Status = valueobjects.AreaStatusInactive
	area.UpdatedAt = time.Now()

	// Test: Update area
	err = repo.Update(ctx, area)
	assert.NoError(t, err)

	// Test: Verify area was updated
	updatedArea, err := repo.FindByID(ctx, area.ID)
	assert.NoError(t, err)
	assert.Equal(t, "Updated Area Name", updatedArea.Name)
	assert.Equal(t, valueobjects.AreaStatusInactive, updatedArea.Status)

	// Test: Update non-existent area
	nonExistentArea := createTestArea(t, valueobjects.AreaTypeCity)
	nonExistentArea.ID = uuid.New()
	err = repo.Update(ctx, nonExistentArea)
	assert.Error(t, err)
}

func TestAreaRepository_Delete(t *testing.T) {
	repo, cleanup := setupAreaRepo(t)
	defer cleanup()

	ctx := context.Background()
	area := createTestArea(t, valueobjects.AreaTypeCity)

	// Create area first
	err := repo.Save(ctx, area)
	require.NoError(t, err)

	// Test: Delete area
	err = repo.Delete(ctx, area.ID)
	assert.NoError(t, err)

	// Test: Verify area is deleted (should not be found)
	_, err = repo.FindByID(ctx, area.ID)
	assert.Error(t, err)

	// Test: Delete non-existent area
	err = repo.Delete(ctx, uuid.New())
	assert.Error(t, err)
}
