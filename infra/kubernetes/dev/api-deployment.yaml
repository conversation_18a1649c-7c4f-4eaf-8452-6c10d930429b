apiVersion: apps/v1
kind: Deployment
metadata:
  name: core-api
  namespace: torra-dev
  labels:
    app: core-api
    service: core
    component: api
    version: v1

spec:
  replicas: 1
  selector:
    matchLabels:
      app: core-api
  template:
    metadata:
      labels:
        app: core-api
        service: core
        component: api
        version: v1

    spec:
      containers:
        - name: core-api
          image: ghcr.io/paradoxe35/torra-core-api:dev
          imagePullPolicy: Always
          ports:
            - containerPort: 8080
              name: http
              protocol: TCP

          resources:
            requests:
              memory: "128Mi"
              cpu: "100m"
            limits:
              memory: "512Mi"
              cpu: "500m"

          securityContext:
            allowPrivilegeEscalation: false
            runAsNonRoot: true
            runAsUser: 1001
            runAsGroup: 1001
            readOnlyRootFilesystem: false
            capabilities:
              drop:
                - ALL

      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      securityContext:
        fsGroup: 1001
