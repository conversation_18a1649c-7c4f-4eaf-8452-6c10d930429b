name: PR Validation

on:
  pull_request:
    types: [opened, synchronize, reopened]

permissions:
  contents: read
  issues: write
  pull-requests: write

env:
  GO_VERSION: "1.25"
  MIN_COVERAGE: 80

jobs:
  # Check PR title and description
  pr-check:
    name: PR Standards Check
    runs-on: arc-runner-set
    steps:
      - name: Check PR title
        uses: amannn/action-semantic-pull-request@v5
        if: false
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          types: |
            feat
            fix
            docs
            style
            refactor
            perf
            test
            chore
            ci
            build
            revert
          requireScope: true
          subjectPattern: ^(?![A-Z]).+$
          subjectPatternError: |
            The subject "{subject}" found in the pull request title "{title}"
            didn't match the configured pattern. Please ensure that the subject
            doesn't start with an uppercase character.

      - name: Check PR size and add labels
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const { owner, repo, number } = context.issue;

            // Get PR diff stats
            const { data: pr } = await github.rest.pulls.get({
              owner,
              repo,
              pull_number: number
            });

            const additions = pr.additions;
            const deletions = pr.deletions;
            const changes = additions + deletions;

            // Define size thresholds
            const sizes = [
              { label: 'size/xs', max: 10 },
              { label: 'size/s', max: 100 },
              { label: 'size/m', max: 500 },
              { label: 'size/l', max: 1000 },
              { label: 'size/xl', max: Infinity }
            ];

            // Determine size label
            let sizeLabel = 'size/xl';
            for (const size of sizes) {
              if (changes <= size.max) {
                sizeLabel = size.label;
                break;
              }
            }

            // Get current labels
            const { data: currentLabels } = await github.rest.issues.listLabelsOnIssue({
              owner,
              repo,
              issue_number: number
            });

            // Remove existing size labels
            const sizeLabels = currentLabels.filter(l => l.name.startsWith('size/'));
            for (const label of sizeLabels) {
              await github.rest.issues.removeLabel({
                owner,
                repo,
                issue_number: number,
                name: label.name
              }).catch(() => {}); // Ignore if label doesn't exist
            }

            // Add new size label
            await github.rest.issues.addLabels({
              owner,
              repo,
              issue_number: number,
              labels: [sizeLabel]
            }).catch(() => {
              console.log(`Label ${sizeLabel} might not exist in the repo`);
            });

            console.log(`PR has ${changes} changes (${additions} additions, ${deletions} deletions)`);
            console.log(`Applied label: ${sizeLabel}`);

  # Run tests with coverage check
  test-coverage:
    name: Test Coverage Check
    runs-on: arc-runner-set
    services:
      postgres:
        image: postgres:16
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_USER: postgres
          POSTGRES_DB: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Go Environment
        uses: ./.github/actions/setup-go-env
        with:
          go-version: ${{ env.GO_VERSION }}
          cache-key-suffix: "pr-check"

      - name: Run tests with coverage
        id: coverage
        run: |
          # Make script executable and run it
          chmod +x .github/scripts/run-tests-with-coverage.sh

          # Run the coverage script
          # The script will set the coverage output and exit with non-zero if coverage is below minimum
          .github/scripts/run-tests-with-coverage.sh || COVERAGE_EXIT_CODE=$?

          # Don't fail the build for now if coverage is low, just warn
          # Remove this condition to enforce minimum coverage
          if [ "${COVERAGE_EXIT_CODE:-0}" -ne 0 ]; then
            echo "⚠️ Coverage check failed but continuing build"
          fi
        env:
          MIN_COVERAGE: ${{ env.MIN_COVERAGE }}
          # Database testing environment variables
          ENABLE_DB_TESTS: "true"
          CI: "true"
          GITHUB_ACTIONS: "true"
          TEST_DB_HOST: "localhost"
          TEST_DB_PORT: "5432"
          TEST_DB_USER: "postgres"
          TEST_DB_PASSWORD: "postgres"
          TEST_DB_SSL_MODE: "disable"
          TEST_DB_ISOLATE: "true"
          TEST_DB_MIGRATE: "true"

      - name: Generate coverage badge
        if: always()
        run: |
          COVERAGE=${{ steps.coverage.outputs.coverage }}

          # Determine color based on coverage percentage
          if (( $(echo "$COVERAGE >= 90" | bc -l) )); then
            COLOR="4c1"
          elif (( $(echo "$COVERAGE >= 80" | bc -l) )); then
            COLOR="97ca00"
          elif (( $(echo "$COVERAGE >= 70" | bc -l) )); then
            COLOR="a4a61d"
          elif (( $(echo "$COVERAGE >= 60" | bc -l) )); then
            COLOR="dfb317"
          elif (( $(echo "$COVERAGE >= 50" | bc -l) )); then
            COLOR="fe7d37"
          else
            COLOR="e05d44"
          fi

          # Generate badge using shields.io
          mkdir -p badges
          curl -s "https://img.shields.io/badge/Coverage-${COVERAGE}%25-${COLOR}" > badges/coverage.svg

          echo "Coverage badge generated: ${COVERAGE}% with color #${COLOR}"

      - name: Comment PR with coverage
        if: always()
        uses: actions/github-script@v7
        with:
          script: |
            const coverage = '${{ steps.coverage.outputs.coverage }}';
            const minCoverage = '${{ env.MIN_COVERAGE }}';
            const status = parseFloat(coverage) >= parseFloat(minCoverage) ? '✅' : '❌';

            const body = `## Test Coverage Report ${status}

            **Current Coverage:** ${coverage}%
            **Required Coverage:** ${minCoverage}%

            <details>
            <summary>Coverage Details</summary>

            \`\`\`
            ${{ steps.coverage.outputs.details }}
            \`\`\`
            </details>`;

            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: body
            });

  # Code quality analysis
  code-quality:
    name: Code Quality Analysis
    runs-on: arc-runner-set
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Go
        uses: actions/setup-go@v5
        with:
          go-version: ${{ env.GO_VERSION }}

      - name: Run code complexity analysis
        run: |
          go install github.com/fzipp/gocyclo/cmd/gocyclo@latest
          echo "### Cyclomatic Complexity Report" >> complexity-report.md
          echo "Functions with complexity > 10:" >> complexity-report.md
          gocyclo -over 10 . | tee -a complexity-report.md || true

      - name: Run code duplication check
        run: |
          go install github.com/mibk/dupl@latest
          echo "### Code Duplication Report" >> duplication-report.md
          dupl -t 100 . | tee -a duplication-report.md || true

      - name: Check for TODOs and FIXMEs
        run: |
          echo "### TODOs and FIXMEs" >> todo-report.md
          grep -rn "TODO\|FIXME" --include="*.go" . | tee -a todo-report.md || true

      - name: Upload reports
        uses: actions/upload-artifact@v4
        with:
          name: code-quality-reports
          path: |
            complexity-report.md
            duplication-report.md
            todo-report.md

  # Documentation check
  docs-check:
    name: Documentation Check
    runs-on: arc-runner-set
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Go Environment
        uses: ./.github/actions/setup-go-env
        with:
          go-version: ${{ env.GO_VERSION }}
          cache-key-suffix: "docs-check"

      - name: Check Go doc comments
        run: |
          go install golang.org/x/tools/cmd/godoc@latest
          # Check for missing doc comments on exported types and functions
          output=$(go list -f '{{.Dir}}' ./... | xargs -I {} sh -c 'go doc -all {} 2>&1 | grep -E "^func|^type|^var|^const" | grep -v "//" || true')
          if [ -n "$output" ]; then
            echo "⚠️ Found exported symbols without documentation:"
            echo "$output"
          fi

      - name: Check README files
        run: |
          # Ensure each service has a README
          for service in services/*/; do
            if [ -d "$service" ] && [ ! -f "${service}README.md" ]; then
              echo "❌ Missing README.md in $service"
              exit 1
            fi
          done

      - name: Setup API Documentation
        uses: ./.github/actions/setup-api-docs
        with:
          validate-docs: "true"
          check-up-to-date: "true"

  # Dependencies check
  dependencies:
    name: Dependencies Check
    runs-on: arc-runner-set
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Go
        uses: actions/setup-go@v5
        with:
          go-version: ${{ env.GO_VERSION }}

      - name: Check for outdated dependencies
        run: |
          go list -u -m all | grep '\[' || echo "All dependencies are up to date"

      # TODO: Enable when needed for dependency analysis
      # - name: Check for unused dependencies
      #   run: |
      #     go install github.com/kisielk/godepgraph@latest
      #     go mod graph | godepgraph -s -o deps.dot
      #     echo "Dependency graph generated in deps.dot"

      # TODO: Enable when license compliance is required
      # - name: License check
      #   run: |
      #     go install github.com/google/go-licenses@latest
      #     go-licenses check ./... --disallowed_types=forbidden,restricted

  # i18n validation
  i18n-validation:
    name: i18n Keys Validation
    runs-on: arc-runner-set
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Go Environment
        uses: ./.github/actions/setup-go-env
        with:
          go-version: ${{ env.GO_VERSION }}
          cache-key-suffix: "i18n-validation"

      - name: Setup i18n Validation
        uses: ./.github/actions/setup-i18n-validation
        with:
          fail-on-missing: "true"
          check-unused: "true"

      - name: Comment PR with i18n validation results
        if: always()
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const { execSync } = require('child_process');

            try {
              // Run validation and capture output
              const output = execSync('make i18n-validate 2>&1', { encoding: 'utf8' });

              const body = `## i18n Validation Report ✅

              All i18n keys are properly validated!

              <details>
              <summary>Validation Details</summary>

              \`\`\`
              ${output}
              \`\`\`
              </details>`;

              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: body
              });
            } catch (error) {
              const body = `## i18n Validation Report ❌

              **Validation failed!** Please fix the following i18n key issues:

              <details>
              <summary>Error Details</summary>

              \`\`\`
              ${error.stdout || error.message}
              \`\`\`
              </details>

              **How to fix:**
              1. Add missing keys to locale files in \`packages/i18n/locales/\`
              2. Remove unused i18n function calls
              3. Run \`make i18n-validate\` locally to verify fixes`;

              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: body
              });
            }
