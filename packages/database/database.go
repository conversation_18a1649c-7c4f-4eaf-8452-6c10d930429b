package database

import (
	"context"
	"database/sql"
	"fmt"
	"os"
	"strconv"
	"time"

	"github.com/jmoiron/sqlx"
	_ "github.com/lib/pq"

	"github.com/paradoxe35/torra/packages/config"
)

type DB struct {
	*sqlx.DB
}

func New(cfg *config.DatabaseConfig) (*DB, error) {
	db, err := sqlx.Connect("postgres", cfg.DSN())
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	db.SetMaxOpenConns(cfg.MaxOpenConns)
	db.SetMaxIdleConns(cfg.MaxIdleConns)
	db.SetConnMaxLifetime(cfg.ConnMaxLifetime)

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := db.PingContext(ctx); err != nil {
		return nil, fmt.<PERSON><PERSON><PERSON>("failed to ping database: %w", err)
	}

	return &DB{db}, nil
}

func (db *DB) InTransaction(ctx context.Context, fn func(*sqlx.Tx) error) error {
	tx, err := db.BeginTxx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}

	defer func() {
		if p := recover(); p != nil {
			tx.Rollback()
			panic(p)
		} else if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit()
		}
	}()

	err = fn(tx)
	return err
}

func (db *DB) ExistsContext(ctx context.Context, query string, args ...interface{}) (bool, error) {
	var exists bool
	query = fmt.Sprintf("SELECT EXISTS(%s)", query)
	err := db.GetContext(ctx, &exists, query, args...)
	return exists, err
}

type Pagination struct {
	Page    int
	PerPage int
}

func (p *Pagination) Offset() int {
	if p.Page <= 0 {
		p.Page = 1
	}
	if p.PerPage <= 0 {
		p.PerPage = 20
	}
	return (p.Page - 1) * p.PerPage
}

func (p *Pagination) Limit() int {
	if p.PerPage <= 0 {
		return 20
	}
	if p.PerPage > 100 {
		return 100
	}
	return p.PerPage
}

type QueryBuilder struct {
	query string
	args  []interface{}
}

func NewQueryBuilder(base string) *QueryBuilder {
	return &QueryBuilder{
		query: base,
		args:  make([]interface{}, 0),
	}
}

func (qb *QueryBuilder) Where(condition string, args ...interface{}) *QueryBuilder {
	qb.query += " WHERE " + condition
	qb.args = append(qb.args, args...)
	return qb
}

func (qb *QueryBuilder) And(condition string, args ...interface{}) *QueryBuilder {
	qb.query += " AND " + condition
	qb.args = append(qb.args, args...)
	return qb
}

func (qb *QueryBuilder) OrderBy(order string) *QueryBuilder {
	qb.query += " ORDER BY " + order
	return qb
}

func (qb *QueryBuilder) Limit(limit int) *QueryBuilder {
	qb.query += fmt.Sprintf(" LIMIT %d", limit)
	return qb
}

func (qb *QueryBuilder) Offset(offset int) *QueryBuilder {
	qb.query += fmt.Sprintf(" OFFSET %d", offset)
	return qb
}

func (qb *QueryBuilder) Build() (string, []interface{}) {
	return qb.query, qb.args
}

func IsNoRowsError(err error) bool {
	return err == sql.ErrNoRows
}

// TestDB represents a test database connection with cleanup capabilities
type TestDB struct {
	*DB
	dbName   string
	cleanup  func() error
	migrated bool
}

// TestConfig holds configuration for test database setup
type TestConfig struct {
	Host     string
	Port     int
	User     string
	Password string
	SSLMode  string
	// If true, creates a unique database for each test
	IsolateTests bool
	// If true, runs database migrations
	RunMigrations bool
	// Migration path (optional, defaults to looking for migrations)
	MigrationPath string
}

// DefaultTestConfig returns a default test configuration
func DefaultTestConfig() *TestConfig {
	return &TestConfig{
		Host:          getEnvOrDefault("TEST_DB_HOST", "localhost"),
		Port:          getEnvOrDefaultInt("TEST_DB_PORT", 5432),
		User:          getEnvOrDefault("TEST_DB_USER", "postgres"),
		Password:      getEnvOrDefault("TEST_DB_PASSWORD", "postgres"),
		SSLMode:       getEnvOrDefault("TEST_DB_SSL_MODE", "disable"),
		IsolateTests:  getEnvOrDefaultBool("TEST_DB_ISOLATE", true),
		RunMigrations: getEnvOrDefaultBool("TEST_DB_MIGRATE", true),
		MigrationPath: getEnvOrDefault("TEST_DB_MIGRATION_PATH", ""),
	}
}

// Helper functions for environment variables
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvOrDefaultInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvOrDefaultBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

// IsTestingEnabled checks if database testing is enabled via environment variables
func IsTestingEnabled() bool {
	return getEnvOrDefaultBool("ENABLE_DB_TESTS", false) ||
		getEnvOrDefaultBool("CI", false) ||
		getEnvOrDefaultBool("GITHUB_ACTIONS", false)
}

// SetupTestDB creates a test database connection with optional isolation and migrations
func SetupTestDB(cfg *TestConfig) (*TestDB, error) {
	if cfg == nil {
		cfg = DefaultTestConfig()
	}

	var dbName string
	var cleanup func() error

	if cfg.IsolateTests {
		// Create a unique database for this test
		uniqueDBName := fmt.Sprintf("test_torra_%d", time.Now().UnixNano())
		if err := createTestDatabase(cfg, uniqueDBName); err != nil {
			return nil, fmt.Errorf("failed to create test database: %w", err)
		}
		dbName = uniqueDBName
		cleanup = func() error {
			return dropTestDatabase(cfg, uniqueDBName)
		}
	} else {
		// Use a shared test database
		dbName = getEnvOrDefault("TEST_DB_NAME", "test_torra")
		if err := createTestDatabase(cfg, dbName); err != nil {
			// Database might already exist, which is fine for shared mode
		}
		cleanup = func() error {
			// Don't drop shared database, just clean tables
			return cleanTestDatabase(cfg, dbName)
		}
	}

	// Connect to the test database
	dbConfig := &config.DatabaseConfig{
		Host:            cfg.Host,
		Port:            cfg.Port,
		User:            cfg.User,
		Password:        cfg.Password,
		Database:        dbName,
		SSLMode:         cfg.SSLMode,
		MaxOpenConns:    5, // Lower for tests
		MaxIdleConns:    2, // Lower for tests
		ConnMaxLifetime: 5 * time.Minute,
	}

	db, err := New(dbConfig)
	if err != nil {
		if cleanup != nil {
			cleanup() // Clean up on failure
		}
		return nil, fmt.Errorf("failed to connect to test database: %w", err)
	}

	testDB := &TestDB{
		DB:      db,
		dbName:  dbName,
		cleanup: cleanup,
	}

	// Run migrations if requested
	if cfg.RunMigrations {
		if err := testDB.runMigrations(cfg.MigrationPath); err != nil {
			testDB.Close()
			return nil, fmt.Errorf("failed to run migrations: %w", err)
		}
		testDB.migrated = true
	}

	return testDB, nil
}

// Close closes the test database connection and runs cleanup
func (tdb *TestDB) Close() error {
	if tdb.DB != nil {
		if err := tdb.DB.Close(); err != nil {
			return fmt.Errorf("failed to close database connection: %w", err)
		}
	}

	if tdb.cleanup != nil {
		if err := tdb.cleanup(); err != nil {
			return fmt.Errorf("failed to cleanup test database: %w", err)
		}
	}

	return nil
}

// GetDBName returns the test database name
func (tdb *TestDB) GetDBName() string {
	return tdb.dbName
}

// IsMigrated returns true if migrations were run on this test database
func (tdb *TestDB) IsMigrated() bool {
	return tdb.migrated
}

// createTestDatabase creates a new test database
func createTestDatabase(cfg *TestConfig, dbName string) error {
	// Connect to postgres database to create the test database
	adminConfig := &config.DatabaseConfig{
		Host:     cfg.Host,
		Port:     cfg.Port,
		User:     cfg.User,
		Password: cfg.Password,
		Database: "postgres", // Connect to default postgres database
		SSLMode:  cfg.SSLMode,
	}

	adminDB, err := New(adminConfig)
	if err != nil {
		return fmt.Errorf("failed to connect to admin database: %w", err)
	}
	defer adminDB.Close()

	// Create the test database
	_, err = adminDB.Exec(fmt.Sprintf("CREATE DATABASE %s", dbName))
	if err != nil {
		// Check if database already exists
		if !isDBExistsError(err) {
			return fmt.Errorf("failed to create test database %s: %w", dbName, err)
		}
	}

	return nil
}

// dropTestDatabase drops a test database
func dropTestDatabase(cfg *TestConfig, dbName string) error {
	// Connect to postgres database to drop the test database
	adminConfig := &config.DatabaseConfig{
		Host:     cfg.Host,
		Port:     cfg.Port,
		User:     cfg.User,
		Password: cfg.Password,
		Database: "postgres", // Connect to default postgres database
		SSLMode:  cfg.SSLMode,
	}

	adminDB, err := New(adminConfig)
	if err != nil {
		return fmt.Errorf("failed to connect to admin database: %w", err)
	}
	defer adminDB.Close()

	// Terminate connections to the database before dropping
	_, err = adminDB.Exec(fmt.Sprintf(`
		SELECT pg_terminate_backend(pid)
		FROM pg_stat_activity
		WHERE datname = '%s' AND pid <> pg_backend_pid()
	`, dbName))
	if err != nil {
		// Log but don't fail - this is best effort
	}

	// Drop the test database
	_, err = adminDB.Exec(fmt.Sprintf("DROP DATABASE IF EXISTS %s", dbName))
	if err != nil {
		return fmt.Errorf("failed to drop test database %s: %w", dbName, err)
	}

	return nil
}
