{"auth.welcome": "Bienvenue sur MyTorra", "auth.login_success": "Connexion réussie", "auth.logout_success": "Déconnexion réussie", "auth.registration_success": "Inscription réussie! Veuillez vérifier votre email.", "auth.invalid_credentials": "Email ou mot de passe incorrect", "auth.account_suspended": "Votre compte a été suspendu", "auth.account_not_verified": "Veuillez vérifier votre adresse email", "auth.mfa_required": "Authentification à deux facteurs requise", "auth.mfa_invalid": "Code de vérification invalide", "auth.password_reset_sent": "Instructions de réinitialisation envoyées à votre email", "auth.password_reset_success": "Réinitialisation du mot de passe réussie", "auth.email_verified": "Email vérifié avec succès", "auth.phone_verified": "Numéro de téléphone vérifié avec succès", "auth.invalid_password": "Mot de passe invalide", "user.not_found": "Utilisateur non trouvé", "user.already_exists": "Un utilisateur avec cet email existe déjà", "user.profile_updated": "Profil mis à jour avec succès", "user.area_updated": "Zone mise à jour avec succès", "user.verification_updated": "Statut de vérification mis à jour", "area.not_found": "Zone non trouvée", "area.not_active": "La zone sélectionnée n'est pas active", "area.invalid": "Zone sélectionnée invalide", "area.service_unavailable": "Service non disponible dans cette zone", "payment.success": "Paiement traité avec succès", "payment.failed": "Le paiement a échoué", "payment.insufficient_funds": "Fonds insuffisants", "payment.method_not_supported": "Méthode de paiement non supportée", "payment.refund_success": "Remboursement traité avec succès", "payment.refund_failed": "Le remboursement a échoué", "subscription.created": "Abonnement créé avec succès", "subscription.upgraded": "Abonnement mis à niveau avec succès", "subscription.downgraded": "Abonnement rétrogradé avec succès", "subscription.cancelled": "Abonnement annulé", "subscription.paused": "Abonnement mis en pause", "subscription.resumed": "Abonnement repris", "subscription.expired": "Abonnement expiré", "subscription.limit_reached": "Limite d'utilisation atteinte pour %s", "notification.sent": "Notification envoyée avec succès", "notification.preferences_updated": "Préférences de notification mises à jour", "validation.required": "Ce champ est obligatoire", "validation.email": "Veu<PERSON>z entrer une adresse email valide", "validation.phone": "Veuillez entrer un numéro de téléphone valide (format E.164)", "validation.password.min": "Le mot de passe doit contenir au moins 12 caractères", "validation.password.uppercase": "Le mot de passe doit contenir au moins une majuscule", "validation.password.lowercase": "Le mot de passe doit contenir au moins une minuscule", "validation.password.number": "Le mot de passe doit contenir au moins un chiffre", "validation.password.special": "Le mot de passe doit contenir au moins un caractère spécial", "validation.min_length": "Doit contenir au moins %d caractères", "validation.max_length": "Ne doit pas dépasser %d caractères", "validation.area_invalid": "Zone sélectionnée invalide", "validation.terms_required": "<PERSON><PERSON> de<PERSON> accepter les conditions générales", "validation.password_mismatch": "Les mots de passe ne correspondent pas", "validation.invalid_input": "Entrée invalide fournie", "validation.invalid_code": "Code de vérification invalide", "validation.password_or_mfa_required": "Mot de passe ou code MFA requis", "mfa.already_enabled": "L'authentification à deux facteurs est déjà activée", "mfa.not_enabled": "L'authentification à deux facteurs n'est pas activée", "mfa.invalid_code": "Code d'authentification à deux facteurs invalide", "verification.already_verified": "Déjà vérifié", "error.internal": "Une erreur interne s'est produite. Veuillez réessayer plus tard.", "error.unauthorized": "Vous n'êtes pas autorisé à effectuer cette action", "error.forbidden": "Accès interdit", "error.not_found": "Ressource non trouvée", "error.bad_request": "<PERSON><PERSON><PERSON><PERSON><PERSON> invalide", "error.rate_limit": "Trop de requêtes. Veuillez réessayer plus tard.", "error.service_unavailable": "Service temporairement indisponible", "error.invalid_request_body": "Corps de requête invalide", "error.invalid_user_id": "ID utilisateur invalide", "error.invalid_token": "Token de vérification requis", "error.file_required": "Image de pièce d'identité requise", "error.invalid_form_data": "Échec de l'analyse des données du formulaire", "area.no_location_data": "Aucune coordonnée GPS fournie et impossible de déterminer l'IP client", "area.detection_failed": "Impossible de détecter la zone à partir des données de localisation fournies", "area.fetch_failed": "Échec de la récupération des zones", "auth.registration_failed": "Échec de l'inscription", "auth.login_failed": "Identifiants invalides", "auth.logout_failed": "Échec de la déconnexion", "auth.logout_success_message": "Déconnexion réussie", "auth.refresh_failed": "Token de rafraîchissement invalide", "auth.request_failed": "Échec de la requête", "auth.password_reset_email_sent": "Email de réinitialisation du mot de passe envoyé", "auth.password_reset_success_message": "Réinitialisation du mot de passe réussie", "auth.email_verified_message": "Email vérifié avec succès", "auth.phone_verified_message": "Numéro de téléphone vérifié avec succès", "auth.verification_failed": "Échec de la vérification", "user.update_failed": "Échec de la mise à jour", "user.delete_failed": "Échec de la suppression", "user.deleted_successfully": "Utilisateur supprimé avec succès", "user.mfa_enable_failed": "Échec de l'activation de l'authentification à deux facteurs", "user.mfa_disable_failed": "Échec de la désactivation de l'authentification à deux facteurs", "user.mfa_disabled_successfully": "Authentification à deux facteurs désactivée avec succès", "user.government_id_verification_initiated": "Vérification de la pièce d'identité initiée", "jwks.internal_server_error": "<PERSON>rreur interne du serveur", "jwks.no_active_key_found": "Aucune clé active trouvée", "auth.missing_token": "En-tête d'autorisation requis", "auth.invalid_token_format": "Format d'en-tête d'autorisation invalide", "auth.invalid_token": "Format de token invalide", "auth.missing_key_id": "ID de clé manquant dans le token", "auth.invalid_key": "Clé de signature invalide", "auth.invalid_or_expired_token": "Token invalide ou expiré", "auth.invalid_claims": "Revendications de token invalides"}